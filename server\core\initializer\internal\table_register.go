package internal

import (
	"os"
	"server/global"
	"server/model/basic"
	"server/model/sys"
	"server/model/task"

	"go.uber.org/zap"
)

func RegistTable() {
	db := global.DB
	err := db.AutoMigrate(
		basic.Proxy{},
		sys.SysUser{},
		sys.SysOperationRecord{},
		sys.Listener{},
		sys.Client{},
		sys.HeartbeatConfig{},
		sys.SysNotification{},
		task.FileTransferTask{},
		task.ProcessTask{},
		task.ScreenshotTask{},
		task.TimedScreenshotTask{},
		task.CronTask{},
		task.CronExecution{},
		task.CronTemplate{},
		task.NetworkTask{},
		task.ProxyTask{},
		task.TerminalTask{},
	)
	if err != nil {
		global.LOG.Error("注册结构体到数据库中失败", zap.Error(err))
		os.Exit(0)
	}
	global.LOG.Info("注册结构体到数据库中成功")
}
